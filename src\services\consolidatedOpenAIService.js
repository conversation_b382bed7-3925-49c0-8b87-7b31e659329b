import { openaiAPI } from './apiService.js';
import logger from '../utils/logger.js';
import { standardizedErrorHandler } from './standardizedErrorHandler.js';

/**
 * Consolidated OpenAI Integration Service
 * Single service for all AI-related functionality with proper error handling and caching
 */
export class ConsolidatedOpenAIService {
  constructor() {
    this.models = {
      CHAT: 'gpt-3.5-turbo',
      CHAT_16K: 'gpt-3.5-turbo-16k',
      GPT4: 'gpt-4',
      GPT4_TURBO: 'gpt-4-turbo-preview'
    };

    this.defaultConfig = {
      temperature: 0.3,
      max_tokens: 800,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0
    };

    this.prompts = {
      MARKET_ANALYSIS: this.createMarketAnalysisPrompt(),
      SENTIMENT_ANALYSIS: this.createSentimentAnalysisPrompt(),
      TRADING_SIGNAL: this.createTradingSignalPrompt(),
      RISK_ASSESSMENT: this.createRiskAssessmentPrompt(),
      NEWS_SUMMARY: this.createNewsSummaryPrompt()
    };

    this.cache = new Map();
    this.cacheTTL = 10 * 60 * 1000; // 10 minutes
    this.requestQueue = [];
    this.isProcessing = false;
    this.rateLimitDelay = 1000; // 1 second between requests
  }

  /**
   * Generate market analysis using AI
   */
  async generateMarketAnalysis(marketData, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.MARKET_ANALYSIS
        .replace('{SYMBOL}', marketData.symbol || 'Unknown')
        .replace('{TIMEFRAME}', marketData.timeframe || '1D')
        .replace('{PRICE_DATA}', this.formatPriceData(marketData.prices || []))
        .replace('{INDICATORS}', this.formatIndicators(marketData.indicators || {}));

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a professional financial market analyst with expertise in technical and fundamental analysis.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseAnalysisResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'generateMarketAnalysis',
        symbol: marketData.symbol
      });
      throw standardError;
    }
  }

  /**
   * Analyze sentiment from news headlines
   */
  async analyzeSentiment(headlines, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.SENTIMENT_ANALYSIS
        .replace('{HEADLINES}', headlines.map((h, i) => `${i + 1}. ${h}`).join('\n'));

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a financial sentiment analysis expert. Analyze news headlines and provide structured sentiment data.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseSentimentResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'analyzeSentiment',
        headlineCount: headlines.length
      });
      throw standardError;
    }
  }

  /**
   * Generate trading signals
   */
  async generateTradingSignal(marketData, indicators, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.TRADING_SIGNAL
        .replace('{SYMBOL}', marketData.symbol || 'Unknown')
        .replace('{CURRENT_PRICE}', marketData.currentPrice || 'N/A')
        .replace('{INDICATORS}', this.formatIndicators(indicators))
        .replace('{VOLUME}', marketData.volume || 'N/A');

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a trading signal generator. Provide clear buy/sell/hold recommendations with confidence levels and reasoning.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseSignalResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'generateTradingSignal',
        symbol: marketData.symbol
      });
      throw standardError;
    }
  }

  /**
   * Assess risk for a trading position
   */
  async assessRisk(position, marketConditions, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.RISK_ASSESSMENT
        .replace('{POSITION}', JSON.stringify(position))
        .replace('{MARKET_CONDITIONS}', JSON.stringify(marketConditions));

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a risk management expert. Analyze trading positions and provide risk assessments with mitigation strategies.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseRiskResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'assessRisk',
        symbol: position.symbol
      });
      throw standardError;
    }
  }

  /**
   * Summarize news articles
   */
  async summarizeNews(articles, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.NEWS_SUMMARY
        .replace('{ARTICLES}', articles.map((article, i) =>
          `${i + 1}. ${article.title}\n${article.content || article.summary || ''}`
        ).join('\n\n'));

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a financial news analyst. Summarize news articles and extract key market-moving information.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseNewsResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'summarizeNews',
        articleCount: articles.length
      });
      throw standardError;
    }
  }

  /**
   * Make request to OpenAI API with caching and rate limiting
   */
  async makeRequest(requestData) {
    const cacheKey = this.generateCacheKey(requestData);

    // Check cache first
    const cachedResponse = this.cache.get(cacheKey);
    if (cachedResponse && Date.now() - cachedResponse.timestamp < this.cacheTTL) {
      logger.debug('Returning cached OpenAI response');
      return cachedResponse.data;
    }

    // Add to queue for rate limiting
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ requestData, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * Process request queue with rate limiting
   */
  async processQueue() {
    if (this.isProcessing || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.requestQueue.length > 0) {
      const { requestData, resolve, reject } = this.requestQueue.shift();

      try {
        const response = await openaiAPI.post('chat/completions', requestData);

        // Cache the response
        const cacheKey = this.generateCacheKey(requestData);
        this.cache.set(cacheKey, {
          data: response,
          timestamp: Date.now()
        });

        resolve(response);
      } catch (error) {
        reject(error);
      }

      // Rate limiting delay
      if (this.requestQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay));
      }
    }

    this.isProcessing = false;
  }

  /**
   * Generate cache key for request
   */
  generateCacheKey(requestData) {
    const key = JSON.stringify({
      model: requestData.model,
      messages: requestData.messages,
      temperature: requestData.temperature,
      max_tokens: requestData.max_tokens
    });
    return Buffer.from(key).toString('base64').substring(0, 50);
  }

  /**
   * Create market analysis prompt template
   */
  createMarketAnalysisPrompt() {
    return `Analyze the following market data for {SYMBOL} on {TIMEFRAME} timeframe:

Price Data: {PRICE_DATA}
Technical Indicators: {INDICATORS}

Please provide:
1. Current market trend analysis
2. Key support and resistance levels
3. Technical pattern identification
4. Volume analysis
5. Overall market sentiment
6. Trading recommendations with reasoning

Format your response as structured JSON with the following fields:
- trend: "bullish" | "bearish" | "neutral"
- confidence: number (0-100)
- support_levels: array of numbers
- resistance_levels: array of numbers
- patterns: array of pattern names
- recommendation: "buy" | "sell" | "hold"
- reasoning: detailed explanation
- risk_level: "low" | "medium" | "high"`;
  }

  /**
   * Create sentiment analysis prompt template
   */
  createSentimentAnalysisPrompt() {
    return `Analyze the sentiment of these financial news headlines:

{HEADLINES}

Provide a JSON response with:
- overall_sentiment: "bullish" | "bearish" | "neutral"
- sentiment_score: number (-1 to 1, where -1 is very bearish, 1 is very bullish)
- confidence: number (0-100)
- key_themes: array of main themes
- market_impact: "high" | "medium" | "low"
- summary: brief explanation of the sentiment analysis`;
  }

  /**
   * Create trading signal prompt template
   */
  createTradingSignalPrompt() {
    return `Generate a trading signal for {SYMBOL}:

Current Price: {CURRENT_PRICE}
Technical Indicators: {INDICATORS}
Volume: {VOLUME}

Provide a JSON response with:
- signal: "buy" | "sell" | "hold"
- confidence: number (0-100)
- entry_price: suggested entry price
- stop_loss: suggested stop loss level
- take_profit: suggested take profit level
- timeframe: recommended holding period
- reasoning: detailed explanation
- risk_reward_ratio: calculated ratio`;
  }

  /**
   * Create risk assessment prompt template
   */
  createRiskAssessmentPrompt() {
    return `Assess the risk for this trading position:

Position: {POSITION}
Market Conditions: {MARKET_CONDITIONS}

Provide a JSON response with:
- risk_level: "low" | "medium" | "high" | "extreme"
- risk_score: number (0-100)
- key_risks: array of main risk factors
- mitigation_strategies: array of risk mitigation suggestions
- position_sizing: recommended position size
- monitoring_points: key levels to watch
- exit_strategy: recommended exit conditions`;
  }

  /**
   * Create news summary prompt template
   */
  createNewsSummaryPrompt() {
    return `Summarize these financial news articles and extract key market information:

{ARTICLES}

Provide a JSON response with:
- summary: concise summary of all articles
- key_points: array of most important points
- market_impact: potential impact on markets
- affected_sectors: array of sectors that might be affected
- sentiment: overall sentiment from the news
- urgency: "high" | "medium" | "low"
- actionable_insights: trading or investment insights`;
  }

  /**
   * Parse market analysis response
   */
  parseAnalysisResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // Fallback to text parsing
      return {
        trend: this.extractValue(content, 'trend') || 'neutral',
        confidence: parseInt(this.extractValue(content, 'confidence')) || 50,
        recommendation: this.extractValue(content, 'recommendation') || 'hold',
        reasoning: content,
        risk_level: this.extractValue(content, 'risk_level') || 'medium'
      };
    } catch (error) {
      logger.error('Error parsing analysis response:', error);
      return { error: 'Failed to parse analysis response', raw: response };
    }
  }

  /**
   * Parse sentiment analysis response
   */
  parseSentimentResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        overall_sentiment: 'neutral',
        sentiment_score: 0,
        confidence: 50,
        summary: content
      };
    } catch (error) {
      logger.error('Error parsing sentiment response:', error);
      return { error: 'Failed to parse sentiment response', raw: response };
    }
  }

  /**
   * Parse trading signal response
   */
  parseSignalResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        signal: 'hold',
        confidence: 50,
        reasoning: content,
        timeframe: '1D'
      };
    } catch (error) {
      logger.error('Error parsing signal response:', error);
      return { error: 'Failed to parse signal response', raw: response };
    }
  }

  /**
   * Parse risk assessment response
   */
  parseRiskResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        risk_level: 'medium',
        risk_score: 50,
        key_risks: [],
        mitigation_strategies: []
      };
    } catch (error) {
      logger.error('Error parsing risk response:', error);
      return { error: 'Failed to parse risk response', raw: response };
    }
  }

  /**
   * Parse news summary response
   */
  parseNewsResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        summary: content,
        key_points: [],
        market_impact: 'medium',
        sentiment: 'neutral'
      };
    } catch (error) {
      logger.error('Error parsing news response:', error);
      return { error: 'Failed to parse news response', raw: response };
    }
  }

  /**
   * Extract value from text using regex
   */
  extractValue(text, key) {
    const regex = new RegExp(`${key}[:\\s]*([^\\n,}]+)`, 'i');
    const match = text.match(regex);
    return match ? match[1].trim().replace(/['"]/g, '') : null;
  }

  /**
   * Format price data for prompts
   */
  formatPriceData(prices) {
    if (!Array.isArray(prices) || prices.length === 0) {
      return 'No price data available';
    }

    const recent = prices.slice(-10);
    return recent.map(p =>
      `${p.timestamp}: O:${p.open} H:${p.high} L:${p.low} C:${p.close} V:${p.volume}`
    ).join('\n');
  }

  /**
   * Format indicators for prompts
   */
  formatIndicators(indicators) {
    if (!indicators || typeof indicators !== 'object') {
      return 'No indicators available';
    }

    return Object.entries(indicators)
      .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
      .join('\n');
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    logger.info('OpenAI service cache cleared');
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      cacheSize: this.cache.size,
      queueLength: this.requestQueue.length,
      isProcessing: this.isProcessing,
      cacheTTL: this.cacheTTL,
      rateLimitDelay: this.rateLimitDelay
    };
  }
}

// Create singleton instance
export const consolidatedOpenAIService = new ConsolidatedOpenAIService();
export default consolidatedOpenAIService;
